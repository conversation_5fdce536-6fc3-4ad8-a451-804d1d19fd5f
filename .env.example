# Database
DATABASE_URL="postgresql://username:password@localhost:5499/ns_shop?schema=public"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# JWT
JWT_SECRET="your-jwt-secret-here"

# App
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_BUILD_VERSION="1.0.0"

# Email Configuration
# SendGrid (recommended)
SENDGRID_API_KEY="your-sendgrid-api-key-here"
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="NS Shop"

# Legacy SMTP (optional - for fallback)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"

# File Upload (optional)
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE=5242880

# Redis (optional)
REDIS_URL="redis://localhost:6379"

# MinIO (Object Storage)
MINIO_ENDPOINT="localhost"
MINIO_PORT=9000
MINIO_ACCESS_KEY="minioadmin"
MINIO_SECRET_KEY="minioadmin123"
MINIO_BUCKET_NAME="ns-shop-media"
MINIO_USE_SSL=false
