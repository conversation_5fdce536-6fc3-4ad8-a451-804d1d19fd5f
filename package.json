{"name": "ns-shop", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 6002", "build": "yarn p:m && next build", "start": "next start", "lint": "npx tsc --noEmit --skipLibCheck --project .", "p:m": "prisma migrate dev", "p:m:r": "prisma migrate reset", "p:s": "prisma studio", "dup": "docker compose up -d", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/unit", "test:unit:watch": "jest tests/unit --watch", "test:unit:coverage": "jest tests/unit --coverage", "test:integration": "NODE_ENV=test jest tests/integration", "test:integration:watch": "NODE_ENV=test jest tests/integration --watch", "test:integration:coverage": "NODE_ENV=test jest tests/integration --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report", "test:all": "npm run test:unit && npm run test:integration && npm run test:e2e", "test:all:coverage": "npm run test:unit:coverage && npm run test:integration:coverage", "test:attributes": "jest tests/unit/lib/utils/attributes.test.ts tests/unit/api/admin/attributes.test.ts tests/integration/api/admin/attributes.test.ts", "test:attributes:e2e": "playwright test tests/e2e/admin/attributes/", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma db push --force-reset && npm run db:seed", "db:reset:test": "DATABASE_URL=$DATABASE_URL_TEST prisma migrate reset --force"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@hookform/resolvers": "^4.1.3", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@tiptap/core": "^3.0.7", "@tiptap/extension-bullet-list": "^3.0.7", "@tiptap/extension-image": "^3.0.7", "@tiptap/extension-link": "^3.0.7", "@tiptap/extension-list": "^3.0.7", "@tiptap/extension-list-item": "^3.0.7", "@tiptap/extension-ordered-list": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@types/bcryptjs": "^2.4.6", "@types/nodemailer": "^6.4.17", "@types/nprogress": "^0.2.3", "@types/xlsx": "^0.0.35", "bcryptjs": "^3.0.2", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "commander": "^14.0.0", "date-fns": "^4.1.0", "eslint": "^9.30.0", "framer-motion": "^12.6.2", "inquirer": "^12.7.0", "jose": "^6.0.10", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "minio": "^8.0.5", "next": "^15.4.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "nprogress": "^0.2.0", "radix-ui": "^1.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "recharts": "^2.15.3", "sonner": "^2.0.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/inquirer": "^9.0.8", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.20", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint-config-next": "15.2.3", "eslint-config-prettier": "^10.1.1", "jest": "^30.0.3", "jest-environment-jsdom": "^30.0.4", "msw": "^2.10.2", "prisma": "^6.12.0", "tailwindcss": "^4", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5", "typescript-eslint": "^8.28.0", "whatwg-fetch": "^3.6.20"}, "packageManager": "yarn@4.9.2"}