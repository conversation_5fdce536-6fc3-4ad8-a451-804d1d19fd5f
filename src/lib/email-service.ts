import nodemailer from "nodemailer";
import { prisma } from "@/lib/prisma";
import { sendGridEmailService } from "./sendgrid-email-service";
import {
  type WelcomeEmailData,
  type OrderConfirmationEmailData,
  type ContactFormEmailData,
  type PasswordResetEmailData,
  type AdminNotificationEmailData,
} from "./email-templates";

interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
  from: {
    name: string;
    address: string;
  };
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface NotificationEmailData {
  notification: {
    id: string;
    title: string;
    message: string;
    type: string;
    priority: string;
    actionUrl?: string;
    createdAt: string;
  };
  recipient: {
    name: string;
    email: string;
  };
  sender?: {
    name: string;
    email: string;
  };
}

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig | null = null;
  private useSendGrid = false;

  async initialize() {
    try {
      // Try to initialize SendGrid first
      const sendGridInitialized = await sendGridEmailService.initialize();
      if (sendGridInitialized) {
        this.useSendGrid = true;
        console.log("Email service initialized with SendGrid");
        return true;
      }

      // Fallback to Nodemailer if SendGrid is not available
      console.log("SendGrid not available, falling back to Nodemailer");

      // Get email settings from environment or database
      this.config = {
        host: process.env.EMAIL_SERVER_HOST || "smtp.gmail.com",
        port: parseInt(process.env.EMAIL_SERVER_PORT || "587"),
        secure: false, // true for 465, false for other ports
        auth: {
          user: process.env.EMAIL_SERVER_USER || "",
          pass: process.env.EMAIL_SERVER_PASSWORD || "",
        },
        from: {
          name: process.env.EMAIL_FROM_NAME || "NS Shop Admin",
          address: process.env.EMAIL_FROM || "<EMAIL>",
        },
      };

      if (!this.config.auth.user || !this.config.auth.pass) {
        console.warn("Email credentials not configured for Nodemailer");
        return false;
      }

      this.transporter = nodemailer.createTransporter({
        host: this.config.host,
        port: this.config.port,
        secure: this.config.secure,
        auth: this.config.auth,
      });

      // Verify connection
      await this.transporter.verify();
      console.log("Email service initialized with Nodemailer");
      return true;
    } catch (error) {
      console.error("Failed to initialize email service:", error);
      return false;
    }
  }

  private generateNotificationTemplate(
    data: NotificationEmailData
  ): EmailTemplate {
    const { notification, sender } = data;

    const typeColors = {
      INFO: "#3b82f6",
      SUCCESS: "#10b981",
      WARNING: "#f59e0b",
      ERROR: "#ef4444",
      SYSTEM: "#6366f1",
    };

    const priorityLabels = {
      LOW: "Thấp",
      NORMAL: "Bình thường",
      HIGH: "Cao",
      URGENT: "Khẩn cấp",
    };

    const typeLabels = {
      INFO: "Thông tin",
      SUCCESS: "Thành công",
      WARNING: "Cảnh báo",
      ERROR: "Lỗi",
      SYSTEM: "Hệ thống",
    };

    const color =
      typeColors[notification.type as keyof typeof typeColors] || "#6b7280";
    const typeLabel =
      typeLabels[notification.type as keyof typeof typeLabels] ||
      notification.type;
    const priorityLabel =
      priorityLabels[notification.priority as keyof typeof priorityLabels] ||
      notification.priority;

    const subject = `[NS Shop Admin] ${notification.title}`;

    const html = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${notification.title}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background-color: ${color};
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 30px;
        }
        .notification-meta {
            background-color: #f3f4f6;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .meta-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .meta-item:last-child {
            margin-bottom: 0;
        }
        .meta-label {
            font-weight: 600;
            color: #6b7280;
        }
        .meta-value {
            color: #374151;
        }
        .priority-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        .priority-urgent { background-color: #fee2e2; color: #dc2626; }
        .priority-high { background-color: #fef3c7; color: #d97706; }
        .priority-normal { background-color: #e5e7eb; color: #6b7280; }
        .priority-low { background-color: #f3f4f6; color: #9ca3af; }
        .action-button {
            display: inline-block;
            background-color: ${color};
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .footer {
            background-color: #f9fafb;
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
        }
        .footer a {
            color: ${color};
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>NS Shop Admin</h1>
            <p style="margin: 10px 0 0 0; opacity: 0.9;">Thông báo mới</p>
        </div>
        
        <div class="content">
            <h2 style="color: ${color}; margin-top: 0;">${notification.title}</h2>
            
            <div class="notification-meta">
                <div class="meta-item">
                    <span class="meta-label">Loại:</span>
                    <span class="meta-value">${typeLabel}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Độ ưu tiên:</span>
                    <span class="priority-badge priority-${notification.priority.toLowerCase()}">${priorityLabel}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-label">Thời gian:</span>
                    <span class="meta-value">${new Date(notification.createdAt).toLocaleString("vi-VN")}</span>
                </div>
                ${
                  sender
                    ? `
                <div class="meta-item">
                    <span class="meta-label">Người gửi:</span>
                    <span class="meta-value">${sender.name}</span>
                </div>
                `
                    : ""
                }
            </div>
            
            <div style="font-size: 16px; line-height: 1.6; margin: 20px 0;">
                ${notification.message.replace(/\n/g, "<br>")}
            </div>
            
            ${
              notification.actionUrl
                ? `
            <div style="text-align: center; margin: 30px 0;">
                <a href="${notification.actionUrl}" class="action-button">Xem chi tiết</a>
            </div>
            `
                : ""
            }
        </div>
        
        <div class="footer">
            <p>Bạn nhận được email này vì bạn là admin của NS Shop.</p>
            <p>
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/notifications">Quản lý thông báo</a> |
                <a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/settings">Cài đặt</a>
            </p>
        </div>
    </div>
</body>
</html>
    `;

    const text = `
NS Shop Admin - Thông báo mới

${notification.title}

${notification.message}

Loại: ${typeLabel}
Độ ưu tiên: ${priorityLabel}
Thời gian: ${new Date(notification.createdAt).toLocaleString("vi-VN")}
${sender ? `Người gửi: ${sender.name}` : ""}

${notification.actionUrl ? `Xem chi tiết: ${notification.actionUrl}` : ""}

---
Bạn nhận được email này vì bạn là admin của NS Shop.
Quản lý thông báo: ${process.env.NEXT_PUBLIC_APP_URL}/admin/notifications
    `;

    return { subject, html, text };
  }

  async sendNotificationEmail(data: NotificationEmailData): Promise<boolean> {
    try {
      // Use SendGrid if available
      if (this.useSendGrid) {
        const adminNotificationData: AdminNotificationEmailData = {
          recipientName: data.recipient.name,
          recipientEmail: data.recipient.email,
          notification: data.notification,
          sender: data.sender,
        };
        return await sendGridEmailService.sendAdminNotificationEmail(
          adminNotificationData
        );
      }

      // Fallback to Nodemailer
      if (!this.transporter || !this.config) {
        console.warn("Email service not initialized");
        return false;
      }

      const template = this.generateNotificationTemplate(data);

      const mailOptions = {
        from: `${this.config.from.name} <${this.config.from.address}>`,
        to: `${data.recipient.name} <${data.recipient.email}>`,
        subject: template.subject,
        text: template.text,
        html: template.html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log("Notification email sent:", result.messageId);
      return true;
    } catch (error) {
      console.error("Failed to send notification email:", error);
      return false;
    }
  }

  async sendBulkNotificationEmails(
    notifications: NotificationEmailData[]
  ): Promise<{
    sent: number;
    failed: number;
    errors: string[];
  }> {
    // Use SendGrid if available
    if (this.useSendGrid) {
      const adminNotificationData: AdminNotificationEmailData[] =
        notifications.map((notification) => ({
          recipientName: notification.recipient.name,
          recipientEmail: notification.recipient.email,
          notification: notification.notification,
          sender: notification.sender,
        }));

      return await sendGridEmailService.sendBulkAdminNotificationEmails(
        adminNotificationData
      );
    }

    // Fallback to Nodemailer
    const results = {
      sent: 0,
      failed: 0,
      errors: [] as string[],
    };

    for (const notification of notifications) {
      try {
        const success = await this.sendNotificationEmail(notification);
        if (success) {
          results.sent++;
        } else {
          results.failed++;
          results.errors.push(
            `Failed to send to ${notification.recipient.email}`
          );
        }
      } catch (error) {
        results.failed++;
        results.errors.push(
          `Error sending to ${notification.recipient.email}: ${error}`
        );
      }

      // Add small delay to avoid overwhelming the SMTP server
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return results;
  }

  async testConnection(): Promise<boolean> {
    // Use SendGrid if available
    if (this.useSendGrid) {
      return await sendGridEmailService.testConnection();
    }

    // Fallback to Nodemailer
    if (!this.transporter) {
      return false;
    }

    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error("Email connection test failed:", error);
      return false;
    }
  }

  // New methods for different email types
  async sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    if (this.useSendGrid) {
      return await sendGridEmailService.sendWelcomeEmail(data);
    }

    // For Nodemailer fallback, we'd need to implement the template generation
    console.warn("Welcome email not implemented for Nodemailer fallback");
    return false;
  }

  async sendOrderConfirmationEmail(
    data: OrderConfirmationEmailData
  ): Promise<boolean> {
    if (this.useSendGrid) {
      return await sendGridEmailService.sendOrderConfirmationEmail(data);
    }

    console.warn(
      "Order confirmation email not implemented for Nodemailer fallback"
    );
    return false;
  }

  async sendContactFormEmail(data: ContactFormEmailData): Promise<boolean> {
    if (this.useSendGrid) {
      return await sendGridEmailService.sendContactFormEmail(data);
    }

    console.warn("Contact form email not implemented for Nodemailer fallback");
    return false;
  }

  async sendPasswordResetEmail(data: PasswordResetEmailData): Promise<boolean> {
    if (this.useSendGrid) {
      return await sendGridEmailService.sendPasswordResetEmail(data);
    }

    console.warn(
      "Password reset email not implemented for Nodemailer fallback"
    );
    return false;
  }

  async sendTestEmail(
    recipientEmail: string,
    recipientName: string = "Test User"
  ): Promise<boolean> {
    if (this.useSendGrid) {
      return await sendGridEmailService.sendTestEmail(
        recipientEmail,
        recipientName
      );
    }

    // Fallback test email for Nodemailer
    if (!this.transporter || !this.config) {
      console.warn("Email service not initialized");
      return false;
    }

    try {
      const mailOptions = {
        from: `${this.config.from.name} <${this.config.from.address}>`,
        to: `${recipientName} <${recipientEmail}>`,
        subject: "Test Email từ NS Shop",
        text: "Đây là email test để kiểm tra cấu hình email. Nếu bạn nhận được email này, hệ thống email đã hoạt động bình thường.",
        html: `
          <h2>Test Email từ NS Shop</h2>
          <p>Đây là email test để kiểm tra cấu hình email.</p>
          <p>Nếu bạn nhận được email này, hệ thống email đã hoạt động bình thường.</p>
        `,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log("Test email sent:", result.messageId);
      return true;
    } catch (error) {
      console.error("Failed to send test email:", error);
      return false;
    }
  }
}

// Singleton instance
export const emailService = new EmailService();

// Helper function to send notification emails
export async function sendNotificationEmails(
  notificationId: string,
  targetAdmins?: string[]
): Promise<boolean> {
  try {
    // Get notification details
    const notification = await prisma.notification.findUnique({
      where: { id: notificationId },
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!notification) {
      console.error("Notification not found:", notificationId);
      return false;
    }

    // Determine target admins
    let adminQuery: any = {};

    if (targetAdmins && targetAdmins.length > 0) {
      adminQuery.id = { in: targetAdmins };
    } else {
      // Determine based on notification target type
      switch (notification.targetType) {
        case "ALL_ADMINS":
          adminQuery = { isActive: true };
          break;
        case "SPECIFIC_ADMIN":
          if (notification.targetId) {
            adminQuery.id = notification.targetId;
          }
          break;
        case "ROLE_ADMIN":
          adminQuery = { role: "ADMIN", isActive: true };
          break;
        case "ROLE_MODERATOR":
          adminQuery = { role: "MODERATOR", isActive: true };
          break;
        default:
          console.warn("Unknown target type:", notification.targetType);
          return false;
      }
    }

    // Get target admin users
    const admins = await prisma.adminUser.findMany({
      where: adminQuery,
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (admins.length === 0) {
      console.warn("No target admins found for notification:", notificationId);
      return false;
    }

    // Prepare email data
    const emailData: NotificationEmailData[] = admins.map((admin) => ({
      notification: {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type,
        priority: notification.priority,
        actionUrl: notification.actionUrl || undefined,
        createdAt: notification.createdAt.toISOString(),
      },
      recipient: {
        name: admin.name,
        email: admin.email,
      },
      sender: notification.creator
        ? {
            name: notification.creator.name,
            email: notification.creator.email,
          }
        : undefined,
    }));

    // Send emails
    const results = await emailService.sendBulkNotificationEmails(emailData);

    console.log(`Email notification results for ${notificationId}:`, results);

    return results.sent > 0;
  } catch (error) {
    console.error("Failed to send notification emails:", error);
    return false;
  }
}
