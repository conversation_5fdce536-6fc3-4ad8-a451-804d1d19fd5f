"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  Eye, 
  FileText, 
  ExternalLink,
  Loader2,
  CheckCircle,
  XCircle
} from "lucide-react";
import { toast } from "sonner";

interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  previewUrl: string;
}

interface EmailServiceStatus {
  status: string;
  message: string;
  configured: boolean;
  connected: boolean;
}

export default function EmailTemplatesPage() {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [emailStatus, setEmailStatus] = useState<EmailServiceStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [testEmailLoading, setTestEmailLoading] = useState(false);
  const [testEmail, setTestEmail] = useState("");

  useEffect(() => {
    fetchTemplates();
    fetchEmailStatus();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch("/api/admin/email-preview", {
        method: "POST",
      });
      
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates);
      } else {
        toast.error("Không thể tải danh sách template");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tải template");
    } finally {
      setLoading(false);
    }
  };

  const fetchEmailStatus = async () => {
    try {
      const response = await fetch("/api/admin/notifications/email");
      
      if (response.ok) {
        const data = await response.json();
        setEmailStatus(data);
      }
    } catch (error) {
      console.error("Failed to fetch email status:", error);
    }
  };

  const openPreview = (template: EmailTemplate, format: "html" | "text" = "html") => {
    const url = `${template.previewUrl}&format=${format}`;
    window.open(url, "_blank", "width=800,height=600,scrollbars=yes");
  };

  const sendTestEmail = async () => {
    if (!testEmail) {
      toast.error("Vui lòng nhập email để test");
      return;
    }

    setTestEmailLoading(true);
    try {
      const response = await fetch("/api/admin/notifications/email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "test",
          recipient: testEmail,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Email test đã được gửi thành công!");
        setTestEmail("");
      } else {
        toast.error(data.error || "Không thể gửi email test");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi gửi email test");
    } finally {
      setTestEmailLoading(false);
    }
  };

  const getStatusIcon = () => {
    if (!emailStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    
    if (emailStatus.connected) {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    } else {
      return <XCircle className="h-4 w-4 text-red-600" />;
    }
  };

  const getStatusBadge = () => {
    if (!emailStatus) return <Badge variant="secondary">Đang kiểm tra...</Badge>;
    
    if (emailStatus.connected) {
      return <Badge variant="default" className="bg-green-600">Hoạt động</Badge>;
    } else if (emailStatus.configured) {
      return <Badge variant="destructive">Lỗi kết nối</Badge>;
    } else {
      return <Badge variant="secondary">Chưa cấu hình</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Email Templates</h1>
        <p className="text-muted-foreground">
          Xem trước và kiểm tra các template email của hệ thống
        </p>
      </div>

      {/* Email Service Status */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Trạng thái dịch vụ Email
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <p className="font-medium">
                  {emailStatus?.message || "Đang kiểm tra..."}
                </p>
                <p className="text-sm text-muted-foreground">
                  {emailStatus?.configured 
                    ? "Dịch vụ email đã được cấu hình" 
                    : "Dịch vụ email chưa được cấu hình"}
                </p>
              </div>
            </div>
            {getStatusBadge()}
          </div>

          {/* Test Email Section */}
          {emailStatus?.connected && (
            <div className="mt-6 pt-6 border-t">
              <h3 className="font-medium mb-3">Gửi email test</h3>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Nhập email để test..."
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500"
                />
                <Button 
                  onClick={sendTestEmail}
                  disabled={testEmailLoading || !testEmail}
                  className="bg-pink-600 hover:bg-pink-700"
                >
                  {testEmailLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    "Gửi test"
                  )}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Email Templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templates.map((template) => (
          <Card key={template.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-pink-600" />
                {template.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                {template.description}
              </p>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openPreview(template, "html")}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  HTML
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => openPreview(template, "text")}
                  className="flex-1"
                >
                  <FileText className="h-4 w-4 mr-1" />
                  Text
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {templates.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Mail className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Không có template nào
            </h3>
            <p className="text-muted-foreground">
              Không thể tải danh sách email template
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
