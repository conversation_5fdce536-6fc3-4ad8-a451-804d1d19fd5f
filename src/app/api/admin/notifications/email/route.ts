import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { emailService, sendNotificationEmails } from "@/lib/email-service";
import { z } from "zod";

// Validation schema for email notification request
const emailNotificationSchema = z.object({
  notificationId: z.string().min(1, "Notification ID is required"),
  targetAdmins: z.array(z.string()).optional(),
  testMode: z.boolean().optional().default(false),
});

// Validation schema for email test request
const emailTestSchema = z.object({
  recipient: z.string().email("Valid email is required"),
});

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only ADMIN can send email notifications
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only admins can send email notifications" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action === "test") {
      return handleEmailTest(body);
    } else if (action === "send") {
      return handleSendNotification(body);
    } else {
      return NextResponse.json(
        { error: "Invalid action. Use 'test' or 'send'" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Email notification API error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handleEmailTest(body: any) {
  try {
    const { recipient } = emailTestSchema.parse(body);

    // Initialize email service if not already done
    const initialized = await emailService.initialize();
    if (!initialized) {
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 503 }
      );
    }

    // Test connection
    const connectionOk = await emailService.testConnection();
    if (!connectionOk) {
      return NextResponse.json(
        { error: "Email service connection failed" },
        { status: 503 }
      );
    }

    // Send test email
    const testEmailData = {
      notification: {
        id: "test",
        title: "Test Email từ NS Shop Admin",
        message: "Đây là email test để kiểm tra cấu hình email notification. Nếu bạn nhận được email này, hệ thống email đã hoạt động bình thường.",
        type: "INFO",
        priority: "NORMAL",
        createdAt: new Date().toISOString(),
      },
      recipient: {
        name: "Test User",
        email: recipient,
      },
    };

    const success = await emailService.sendNotificationEmail(testEmailData);

    if (success) {
      return NextResponse.json({
        message: "Test email sent successfully",
        recipient,
      });
    } else {
      return NextResponse.json(
        { error: "Failed to send test email" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Email test error:", error);
    return NextResponse.json(
      { error: "Failed to send test email" },
      { status: 500 }
    );
  }
}

async function handleSendNotification(body: any) {
  try {
    const { notificationId, targetAdmins, testMode } = emailNotificationSchema.parse(body);

    // Initialize email service if not already done
    const initialized = await emailService.initialize();
    if (!initialized) {
      return NextResponse.json(
        { error: "Email service not configured" },
        { status: 503 }
      );
    }

    if (testMode) {
      // In test mode, just validate the request without sending
      return NextResponse.json({
        message: "Test mode - email would be sent",
        notificationId,
        targetAdmins: targetAdmins || "auto-determined",
      });
    }

    // Send notification emails
    const success = await sendNotificationEmails(notificationId, targetAdmins);

    if (success) {
      return NextResponse.json({
        message: "Notification emails sent successfully",
        notificationId,
      });
    } else {
      return NextResponse.json(
        { error: "Failed to send notification emails" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Send notification email error:", error);
    return NextResponse.json(
      { error: "Failed to send notification emails" },
      { status: 500 }
    );
  }
}

// GET endpoint to check email service status
export async function GET() {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Initialize email service if not already done
    const initialized = await emailService.initialize();
    
    if (!initialized) {
      return NextResponse.json({
        status: "not_configured",
        message: "Email service not configured",
        configured: false,
        connected: false,
      });
    }

    // Test connection
    const connected = await emailService.testConnection();

    return NextResponse.json({
      status: connected ? "ready" : "connection_failed",
      message: connected ? "Email service ready" : "Email service connection failed",
      configured: true,
      connected,
    });
  } catch (error) {
    console.error("Email service status check error:", error);
    return NextResponse.json({
      status: "error",
      message: "Failed to check email service status",
      configured: false,
      connected: false,
    });
  }
}
