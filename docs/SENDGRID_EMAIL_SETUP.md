# SendGrid Email Setup - NS Shop

## Tổng quan

NS Shop đã được tích hợp với SendGrid để gửi email chuyên nghiệp và đáng tin cậy. Hệ thống email hỗ trợ nhiều loại email khác nhau với template được thiết kế đẹp mắt và responsive.

## Cấu hình

### 1. Biến môi trường

Thêm các biến sau vào file `.env`:

```env
# SendGrid Configuration (Primary)
SENDGRID_API_KEY="your-sendgrid-api-key-here"
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="NS Shop"

# Admin Email
ADMIN_EMAIL="<EMAIL>"

# Legacy SMTP (Fallback - Optional)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
```

### 2. Cài đặt Dependencies

```bash
yarn add @sendgrid/mail
yarn add -D @types/sendgrid
```

## Các loại Email được hỗ trợ

### 1. Welcome Email
- **Khi nào**: Khi người dùng đăng ký tài khoản mới
- **Nội dung**: Chào mừng, thông tin tài khoản, link đăng nhập
- **Template**: `generateWelcomeEmail()`

### 2. Order Confirmation Email
- **Khi nào**: Sau khi đặt hàng thành công
- **Nội dung**: Chi tiết đơn hàng, thông tin giao hàng, link theo dõi
- **Template**: `generateOrderConfirmationEmail()`

### 3. Contact Form Email
- **Khi nào**: Khi khách hàng gửi form liên hệ
- **Nội dung**: Thông tin người gửi, nội dung tin nhắn
- **Template**: `generateContactFormEmail()`

### 4. Password Reset Email
- **Khi nào**: Khi người dùng yêu cầu đặt lại mật khẩu
- **Nội dung**: Link đặt lại mật khẩu, thời gian hết hạn
- **Template**: `generatePasswordResetEmail()`

### 5. Admin Notification Email
- **Khi nào**: Thông báo quan trọng cho admin
- **Nội dung**: Chi tiết thông báo, link hành động
- **Template**: `generateAdminNotificationEmail()`

## Cách sử dụng

### Gửi Welcome Email

```typescript
import { emailService } from '@/lib/email-service';
import type { WelcomeEmailData } from '@/lib/email-templates';

const welcomeData: WelcomeEmailData = {
  recipientName: 'Nguyễn Văn A',
  recipientEmail: '<EMAIL>',
  loginUrl: `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin`,
  supportEmail: '<EMAIL>',
};

await emailService.initialize();
await emailService.sendWelcomeEmail(welcomeData);
```

### Gửi Order Confirmation Email

```typescript
const orderData: OrderConfirmationEmailData = {
  recipientName: 'Nguyễn Văn A',
  recipientEmail: '<EMAIL>',
  order: {
    id: 'order_123',
    total: 500000,
    status: 'PENDING',
    createdAt: new Date().toISOString(),
    items: [
      {
        name: 'Áo thun nam',
        quantity: 2,
        price: 200000,
        image: 'https://example.com/image.jpg',
      }
    ],
    shippingAddress: {
      fullName: 'Nguyễn Văn A',
      address: '123 Đường ABC',
      city: 'Hà Nội',
      postalCode: '100000',
      phone: '0123456789',
    },
  },
  trackingUrl: `${process.env.NEXT_PUBLIC_APP_URL}/orders/order_123`,
};

await emailService.sendOrderConfirmationEmail(orderData);
```

## API Endpoints

### 1. Contact Form API
- **Endpoint**: `POST /api/contact`
- **Mô tả**: Xử lý form liên hệ và gửi email cho admin

### 2. Password Reset API
- **Endpoint**: `POST /api/auth/forgot-password`
- **Mô tả**: Gửi email đặt lại mật khẩu

### 3. Email Preview API (Admin)
- **Endpoint**: `GET /api/admin/email-preview?template=welcome&format=html`
- **Mô tả**: Xem trước email templates

### 4. Email Test API (Admin)
- **Endpoint**: `POST /api/admin/notifications/email`
- **Mô tả**: Gửi email test và kiểm tra kết nối

## Admin Dashboard

### Email Templates Page
- **URL**: `/admin/email-templates`
- **Chức năng**: 
  - Xem trước tất cả email templates
  - Kiểm tra trạng thái dịch vụ email
  - Gửi email test

### Email Status Check
- Kiểm tra kết nối SendGrid
- Hiển thị trạng thái cấu hình
- Test gửi email

## Tính năng nâng cao

### 1. Fallback System
- SendGrid là dịch vụ chính
- Nodemailer SMTP làm backup
- Tự động chuyển đổi khi SendGrid lỗi

### 2. Template System
- HTML templates responsive
- Text fallback cho mọi email
- Branding nhất quán với NS Shop
- Hỗ trợ tiếng Việt

### 3. Error Handling
- Graceful degradation
- Logging chi tiết
- Không làm gián đoạn user flow

## Testing

### Unit Tests
```bash
npm run test:unit -- --testPathPatterns=email
```

### Integration Tests
```bash
npm run test:integration -- --testPathPatterns=contact
```

### Manual Testing
1. Truy cập `/admin/email-templates`
2. Kiểm tra trạng thái email service
3. Gửi email test
4. Xem trước templates

## Troubleshooting

### Lỗi thường gặp

1. **SendGrid API key không hợp lệ**
   - Kiểm tra `SENDGRID_API_KEY` trong `.env`
   - Đảm bảo API key có quyền gửi email

2. **Email không được gửi**
   - Kiểm tra logs trong console
   - Verify domain trong SendGrid
   - Kiểm tra spam folder

3. **Template hiển thị sai**
   - Kiểm tra data truyền vào template
   - Xem trước template qua admin panel

### Debug Commands

```bash
# Kiểm tra email service
curl -X GET http://localhost:3000/api/admin/notifications/email

# Gửi email test
curl -X POST http://localhost:3000/api/admin/notifications/email \
  -H "Content-Type: application/json" \
  -d '{"action": "test", "recipient": "<EMAIL>"}'
```

## Bảo mật

- API keys được lưu trong environment variables
- Email templates được sanitize
- Rate limiting cho email APIs
- Validation đầu vào nghiêm ngặt

## Performance

- Async email sending
- Bulk email support
- Template caching
- Minimal dependencies

## Monitoring

- Email delivery status
- Error tracking
- Performance metrics
- Admin notifications

---

**Lưu ý**: Đảm bảo cấu hình đúng SendGrid API key và verify domain trước khi deploy production.
